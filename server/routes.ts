import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { insertFolderSchema, insertChatSessionSchema, insertMessageSchema, updateChatSessionSchema } from "@shared/schema";
import { z } from "zod";
import { sendFeedbackEmail } from "./feedback";

export async function registerRoutes(app: Express): Promise<Server> {

  // Feedback endpoint
  app.post("/api/feedback", async (req, res) => {
    try {
      const { message } = z.object({ message: z.string().min(1).max(500) }).parse(req.body);
      await sendFeedbackEmail(message);
      res.status(200).json({ success: true, message: "Feedback sent successfully!" });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ success: false, error: "Invalid feedback data", details: error.errors });
      }
      console.error("Feedback submission error:", error);
      res.status(500).json({ success: false, error: "Failed to send feedback." });
    }
  });

  // Folders
  app.get("/api/folders", async (req, res) => {
    try {
      const folders = await storage.getFolders();
      res.json(folders);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch folders" });
    }
  });

  app.post("/api/folders", async (req, res) => {
    try {
      const folderData = insertFolderSchema.parse(req.body);
      const folder = await storage.createFolder(folderData);
      res.json(folder);
    } catch (error) {
      res.status(400).json({ message: "Invalid folder data" });
    }
  });

  app.put("/api/folders/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const folderData = insertFolderSchema.partial().parse(req.body);
      const folder = await storage.updateFolder(id, folderData);
      
      if (!folder) {
        return res.status(404).json({ message: "Folder not found" });
      }
      
      res.json(folder);
    } catch (error) {
      res.status(400).json({ message: "Invalid folder data" });
    }
  });

  app.delete("/api/folders/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const success = await storage.deleteFolder(id);
      
      if (!success) {
        return res.status(404).json({ message: "Folder not found" });
      }
      
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ message: "Failed to delete folder" });
    }
  });

  // Chat Sessions
  app.get("/api/chat-sessions", async (req, res) => {
    try {
      const sessions = await storage.getChatSessions();
      res.json(sessions);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch chat sessions" });
    }
  });

  app.post("/api/chat-sessions", async (req, res) => {
    try {
      const sessionData = insertChatSessionSchema.parse(req.body);
      const session = await storage.createChatSession(sessionData);
      res.json(session);
    } catch (error) {
      res.status(400).json({ message: "Invalid session data" });
    }
  });

  app.put("/api/chat-sessions/:id", async (req, res) => {
    try {
      const id = req.params.id;
      const sessionData = updateChatSessionSchema.parse(req.body);
      const session = await storage.updateChatSession(id, sessionData);
      
      if (!session) {
        return res.status(404).json({ message: "Chat session not found" });
      }
      
      res.json(session);
    } catch (error) {
      res.status(400).json({ message: "Invalid session data" });
    }
  });

  app.delete("/api/chat-sessions/:id", async (req, res) => {
    try {
      const id = req.params.id;
      const success = await storage.deleteChatSession(id);
      
      if (!success) {
        return res.status(404).json({ message: "Chat session not found" });
      }
      
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ message: "Failed to delete chat session" });
    }
  });

  app.post("/api/chat-sessions/:id/move", async (req, res) => {
    try {
      const sessionId = req.params.id;
      const { folderId } = z.object({ folderId: z.number().nullable() }).parse(req.body);
      
      const success = await storage.moveChatSessionToFolder(sessionId, folderId);
      
      if (!success) {
        return res.status(404).json({ message: "Chat session not found" });
      }
      
      res.json({ success: true });
    } catch (error) {
      res.status(400).json({ message: "Invalid move data" });
    }
  });

  // Messages
  app.get("/api/chat-sessions/:id/messages", async (req, res) => {
    try {
      const sessionId = req.params.id;
      const messages = await storage.getMessages(sessionId);
      res.json(messages);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch messages" });
    }
  });

  app.post("/api/chat-sessions/:id/messages", async (req, res) => {
    try {
      const sessionId = req.params.id;
      const messageData = insertMessageSchema.parse({
        ...req.body,
        sessionId,
      });
      
      // Create user message
      const userMessage = await storage.createMessage(messageData);
      
      // Create a simple AI response
      const aiResponseContent = `Thank you for your message: "${messageData.content}". This is a placeholder response while the analytics system is being implemented.`;

      const aiMessage = await storage.createMessage({
        sessionId,
        content: aiResponseContent,
        role: "assistant",
      });

      res.json({ userMessage, aiMessage });
    } catch (error) {
      res.status(400).json({ message: "Invalid message data" });
    }
  });

  app.delete("/api/chat-sessions/:id/messages", async (req, res) => {
    try {
      const sessionId = req.params.id;
      const success = await storage.deleteMessages(sessionId);
      
      if (!success) {
        return res.status(404).json({ message: "Chat session not found" });
      }
      
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ message: "Failed to clear messages" });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
