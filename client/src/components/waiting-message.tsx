import { useWaitingMessages } from "@/hooks/use-waiting-messages";
import { useWaitingMessagesSetting } from "@/hooks/use-settings";

interface WaitingMessageProps {
  isActive: boolean;
  className?: string;
}

export function WaitingMessage({ isActive, className = "" }: WaitingMessageProps) {
  const { waitingMessages, isLoading: isLoadingSettings } = useWaitingMessagesSetting();
  
  const { currentMessage, isRotating } = useWaitingMessages({
    messages: waitingMessages,
    interval: 2500, // 2.5 seconds
    isActive: isActive && !isLoadingSettings,
  });

  if (!isActive || isLoadingSettings) {
    return null;
  }

  return (
    <div className={`flex justify-start ${className}`}>
      <div className="max-w-xs lg:max-w-md xl:max-w-lg">
        <div className="chat-ai-bubble rounded-2xl rounded-bl-md p-4 shadow-lg">
          <div className="flex items-center gap-2">
            <div className="flex gap-1">
              <div className="w-2 h-2 bg-current rounded-full animate-bounce opacity-50"></div>
              <div 
                className="w-2 h-2 bg-current rounded-full animate-bounce opacity-50" 
                style={{ animationDelay: "0.1s" }}
              ></div>
              <div 
                className="w-2 h-2 bg-current rounded-full animate-bounce opacity-50" 
                style={{ animationDelay: "0.2s" }}
              ></div>
            </div>
            <span 
              className={`text-xs opacity-60 transition-opacity duration-300 ${
                isRotating ? 'animate-pulse' : ''
              }`}
            >
              {currentMessage || "AI is thinking..."}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}
