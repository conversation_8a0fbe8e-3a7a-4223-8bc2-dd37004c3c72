import { useState } from "react";
import { Settings, MessageSquare, Save, RotateCcw } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useToast } from "@/hooks/use-toast";
import { useWaitingMessagesSetting } from "@/hooks/use-settings";

interface SettingsDialogProps {
  onClose: () => void;
}

const DEFAULT_WAITING_MESSAGES = [
  "Thinking...",
  "Processing your request...",
  "Almost there...",
  "Analyzing your question...",
  "Gathering information...",
  "Preparing response...",
  "Working on it...",
  "Just a moment...",
  "Computing answer...",
  "Finalizing thoughts..."
];

export function SettingsDialog({ onClose }: SettingsDialogProps) {
  const { toast } = useToast();
  const { waitingMessages, updateWaitingMessages, isUpdating, isLoading } = useWaitingMessagesSetting();
  
  // Convert array to comma-separated string for editing
  const [messagesText, setMessagesText] = useState(() => {
    if (waitingMessages && waitingMessages.length > 0) {
      return waitingMessages.join(", ");
    }
    return DEFAULT_WAITING_MESSAGES.join(", ");
  });

  const handleSaveWaitingMessages = async () => {
    try {
      // Parse the comma-separated messages
      const messages = messagesText
        .split(",")
        .map(msg => msg.trim())
        .filter(msg => msg.length > 0);

      if (messages.length === 0) {
        toast({
          title: "Validation Error",
          description: "Please provide at least one waiting message.",
          variant: "destructive",
        });
        return;
      }

      await updateWaitingMessages(messages);
    } catch (error) {
      // Error handling is done in the hook
    }
  };

  const handleResetToDefaults = () => {
    setMessagesText(DEFAULT_WAITING_MESSAGES.join(", "));
  };

  const previewMessages = messagesText
    .split(",")
    .map(msg => msg.trim())
    .filter(msg => msg.length > 0);

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Settings
          </CardTitle>
          <CardDescription>
            Configure your chatbot settings and preferences
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="waiting-messages" className="w-full">
            <TabsList className="grid w-full grid-cols-1">
              <TabsTrigger value="waiting-messages" className="flex items-center gap-2">
                <MessageSquare className="h-4 w-4" />
                Waiting Messages
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="waiting-messages" className="space-y-4">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="waiting-messages">Waiting Messages</Label>
                  <p className="text-sm text-muted-foreground mb-2">
                    Enter comma-separated messages that will be displayed while the AI is processing responses.
                  </p>
                  <Textarea
                    id="waiting-messages"
                    placeholder="Thinking..., Processing your request..., Almost there..."
                    value={messagesText}
                    onChange={(e) => setMessagesText(e.target.value)}
                    className="min-h-[100px]"
                    disabled={isLoading || isUpdating}
                  />
                </div>

                <div className="flex gap-2">
                  <Button
                    onClick={handleSaveWaitingMessages}
                    disabled={isUpdating || isLoading}
                    className="flex items-center gap-2"
                  >
                    <Save className="h-4 w-4" />
                    {isUpdating ? "Saving..." : "Save Messages"}
                  </Button>
                  
                  <Button
                    variant="outline"
                    onClick={handleResetToDefaults}
                    disabled={isUpdating || isLoading}
                    className="flex items-center gap-2"
                  >
                    <RotateCcw className="h-4 w-4" />
                    Reset to Defaults
                  </Button>
                </div>

                {previewMessages.length > 0 && (
                  <Alert>
                    <MessageSquare className="h-4 w-4" />
                    <AlertDescription>
                      <strong>Preview ({previewMessages.length} messages):</strong>
                      <ul className="mt-2 list-disc list-inside space-y-1">
                        {previewMessages.slice(0, 5).map((message, index) => (
                          <li key={index} className="text-sm">{message}</li>
                        ))}
                        {previewMessages.length > 5 && (
                          <li className="text-sm text-muted-foreground">
                            ...and {previewMessages.length - 5} more
                          </li>
                        )}
                      </ul>
                    </AlertDescription>
                  </Alert>
                )}

                <div className="text-sm text-muted-foreground">
                  <p className="font-medium mb-1">Tips:</p>
                  <ul className="list-disc list-inside space-y-1 text-xs">
                    <li>Messages will rotate every 2.5 seconds while waiting</li>
                    <li>Keep messages short and engaging</li>
                    <li>Use commas to separate multiple messages</li>
                    <li>At least one message is required</li>
                  </ul>
                </div>
              </div>
            </TabsContent>
          </Tabs>

          <div className="flex justify-end mt-6">
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
