import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import type { Setting, InsertSetting, UpdateSetting } from "@shared/schema";

// API base path for production
const API_BASE = import.meta.env.PROD ? "/jurbot-chat/api" : "/api";

interface SettingsApi {
  getSetting: (key: string) => Promise<Setting>;
  setSetting: (setting: InsertSetting) => Promise<Setting>;
  updateSetting: (key: string, setting: UpdateSetting) => Promise<Setting>;
}

const settingsApi: SettingsApi = {
  getSetting: async (key: string): Promise<Setting> => {
    const response = await fetch(`${API_BASE}/settings/${key}`);
    if (!response.ok) {
      throw new Error(`Failed to get setting: ${response.statusText}`);
    }
    return response.json();
  },

  setSetting: async (setting: InsertSetting): Promise<Setting> => {
    const response = await fetch(`${API_BASE}/settings`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(setting),
    });
    if (!response.ok) {
      throw new Error(`Failed to set setting: ${response.statusText}`);
    }
    return response.json();
  },

  updateSetting: async (key: string, setting: UpdateSetting): Promise<Setting> => {
    const response = await fetch(`${API_BASE}/settings/${key}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(setting),
    });
    if (!response.ok) {
      throw new Error(`Failed to update setting: ${response.statusText}`);
    }
    return response.json();
  },
};

export function useSetting(key: string) {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Get setting
  const {
    data: setting,
    isLoading,
    error,
  } = useQuery({
    queryKey: [`${API_BASE}/settings`, key],
    queryFn: () => settingsApi.getSetting(key),
    retry: false, // Don't retry if setting doesn't exist
  });

  // Set/Update setting mutation
  const setSettingMutation = useMutation({
    mutationFn: (value: any) => {
      const settingData: InsertSetting = { key, value };
      return settingsApi.setSetting(settingData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`${API_BASE}/settings`, key] });
      toast({
        title: "Settings saved",
        description: "Your settings have been updated successfully.",
      });
    },
    onError: (error) => {
      toast({
        title: "Error saving settings",
        description: error instanceof Error ? error.message : "Please try again.",
        variant: "destructive",
      });
    },
  });

  return {
    setting,
    isLoading,
    error,
    setSetting: setSettingMutation.mutateAsync,
    isUpdating: setSettingMutation.isPending,
  };
}

export function useWaitingMessagesSetting() {
  const { setting, isLoading, error, setSetting, isUpdating } = useSetting("waiting_messages");

  // Parse the waiting messages from the setting value
  const waitingMessages = setting?.value?.messages || [];

  const updateWaitingMessages = async (messages: string[]) => {
    await setSetting({ messages });
  };

  return {
    waitingMessages,
    isLoading,
    error,
    updateWaitingMessages,
    isUpdating,
  };
}
