import { useState, useEffect, useRef } from "react";

// Default waiting messages
const DEFAULT_WAITING_MESSAGES = [
  "Thinking...",
  "Processing your request...",
  "Almost there...",
  "Analyzing your question...",
  "Gathering information...",
  "Preparing response...",
  "Working on it...",
  "Just a moment...",
  "Computing answer...",
  "Finalizing thoughts..."
];

export interface UseWaitingMessagesOptions {
  messages?: string[];
  interval?: number; // milliseconds between message changes
  isActive?: boolean; // whether to show waiting messages
}

export function useWaitingMessages({
  messages = DEFAULT_WAITING_MESSAGES,
  interval = 2500, // 2.5 seconds
  isActive = false
}: UseWaitingMessagesOptions = {}) {
  const [currentMessageIndex, setCurrentMessageIndex] = useState(0);
  const [currentMessage, setCurrentMessage] = useState("");
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // Parse messages if they come as a comma-separated string
  const parsedMessages = Array.isArray(messages) 
    ? messages 
    : typeof messages === 'string' 
      ? messages.split(',').map(msg => msg.trim()).filter(msg => msg.length > 0)
      : DEFAULT_WAITING_MESSAGES;

  // Ensure we have at least one message
  const finalMessages = parsedMessages.length > 0 ? parsedMessages : DEFAULT_WAITING_MESSAGES;

  useEffect(() => {
    if (isActive && finalMessages.length > 0) {
      // Set initial message
      setCurrentMessage(finalMessages[0]);
      setCurrentMessageIndex(0);

      // Only start rotation if we have more than one message
      if (finalMessages.length > 1) {
        intervalRef.current = setInterval(() => {
          setCurrentMessageIndex((prevIndex) => {
            const nextIndex = (prevIndex + 1) % finalMessages.length;
            setCurrentMessage(finalMessages[nextIndex]);
            return nextIndex;
          });
        }, interval);
      }
    } else {
      // Clear interval and reset when not active
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      setCurrentMessage("");
      setCurrentMessageIndex(0);
    }

    // Cleanup on unmount or dependency change
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [isActive, finalMessages, interval]);

  return {
    currentMessage,
    currentMessageIndex,
    totalMessages: finalMessages.length,
    isRotating: isActive && finalMessages.length > 1
  };
}
